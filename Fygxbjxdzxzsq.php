<?php

namespace app\zt2025\controller;

use app\BaseController;
use app\common\controller\OldController;
use app\common\model\Wechat;
use app\common\controller\ZtController;

use think\facade\View;
use think\facade\Db;

use wechat\Jsauth;
use wechat\Jssdk;

class Fygxbjxdzxzsq extends OldController
{

    public function initialize()
    {
        parent::initialize();
        $this->config_id = 217;
        $this->fid = 485;
        $this->session_id = '2025_fygxbjxdzxzsq_' . $this->fid;
        $this->tableName = 'moo_form_data_2025_3';
        $this->getConfig(); //获取微信配置信息 
        //测试授权
        $this->ztdebug('fygxbjxdzxzsq');
        //授权，key和测试授权保持一致才能起作用
        $this->Oauth('', 'snsapi_userinfo', 'fygxbjxdzxzsq');
        //统计
        totalviews_new($this->fid);

        //活动状态
        if (time() < $this->configWx['reg_start_time']) {
            $this->endtime = 1;
        } elseif (time() > $this->configWx['reg_end_time']) {
            $this->endtime = 2;
        }
    }


    // public function index()
    // {
    //     return view();
    // }
    public function home()
    {
        $info = Db::name($this->tableName)->where('openid', $this->wechat_user['openid'])->where('fid', $this->fid)->find();
        if (!empty($info)) {
            View::assign('name', $info['key_1']);
            View::assign('phone', $info['key_2']);
            View::assign('id_card', $info['key_3']);
            View::assign('certificate_no', $info['key_4']);
            View::assign('rank', $info['key_5']);
        }else{
            View::assign('name', '');
            View::assign('phone', '');
            View::assign('id_card', '');
            View::assign('certificate_no', '');
            View::assign('rank', '');
        }
        return view();
    }

    public function submit()
    {
        if ($this->request->isAjax()) {
            $this->checkdata();
            $info = Db::name($this->tableName)->where('openid', $this->wechat_user['openid'])->where('fid', $this->fid)->find();
            if (!empty($info)) {
                $data = [
                    'fid' => $this->fid,
                    'openid' => '',
                ];
                $key = Db::name($this->tableName)->where('id', $info['id'])->update($data);
                if (empty($key)) {
                    $this->error('网络异常，请重试！');
                }
            }
            if (empty($_POST['name']) || empty($_POST['phone']) || empty($_POST['id_card']) || empty($_POST['certificate_no'])) {
                $this->error('参数丢失！');
            }
            $name = getgpc_new($_POST['name']);
            $phone = getgpc_new($_POST['phone']);
            $id_card = trim($_POST['id_card']);
            $certificate_no = getgpc_new($_POST['certificate_no']);
            $data = [
                'fid' => $this->fid,
                'key_1' => $name,
                'key_2' => $phone,
                'key_3' => $id_card,
            ];
            $certificate_no = "NO." . $certificate_no;
            $info = Db::name($this->tableName)->where($data)->find();
            if (empty($info)) {
                $this->error('未查到您的信息，请重检查信息是否错误！');
            }
            $noarray = explode("/", $info['key_4']);
            if (!in_array($certificate_no, $noarray)) {
                $this->error('未查到您的信息，请重检查信息是否错误！');
            }

            $key = Db::name($this->tableName)->where('id', $info['id'])->update([
                'openid' => $this->wechat_user['openid'],
                'nickname' => $this->wechat_user['nickname'],
                'headimgurl' => $this->wechat_user['headimgurl'],
                'IP' => getIp(0),
                'addtime' => time(),
            ]);
            if ($key) {
                $this->success('提交成功！');
            } else {
                $this->error('提交失败！');
            }
        }
    }

    public function cg()
    {
        $info = Db::name($this->tableName)->where('openid', $this->wechat_user['openid'])->where('fid', $this->fid)->find();
        View::assign('avatar', $this->wechat_user['headimgurl']);
        if (!empty($info)) {
            View::assign('name', $info['key_1']);
            View::assign('phone', $info['key_2']);
            View::assign('id_card', $info['key_3']);
            View::assign('certificate_no', $info['key_4']);
            View::assign('rank', $info['key_5']);
        }else{
            View::assign('name', '');
            View::assign('phone', '');
            View::assign('id_card', '');
            View::assign('certificate_no', '');
            View::assign('rank', '');
        }
        return view();
    }
    public function cw()
    {
        return view();
    }
    public function avatar()
    {
        View::assign('avatar', $this->wechat_user['headimgurl']);
        return view();
    }

    public function daoru()
    {
        set_time_limit(3000); // 设置脚本最大执行时间为 300 秒
        $str = "1	杨可可	15395006605	340111199007307540	NO.12345	第1例
2	吕一	15395006601	340111199007307541	NO.12346	第2例
3	李小康	15395006602	340111199007307542	NO.12347	第3例
4	朱小龙	15395006603	340111199007307543	NO.12348	第4例
5	朱奇奇	15395006604	340111199007307544	NO.12349	第5例
6	刘佳	15395006605	340111199007307546	NO.12350	第6例
7	邓姝	15395006606	340111199007307547	NO.12351	第7例
8	李四	15395006607	340111199007307548	NO.12352	第8例
9	杨可可	15395006605	340111199007307540	NO.12367	第9例
10	王凯	15395006608	340111199007307549	NO.12368	第10例
11	张淑	15395006609	340111199007307550	NO.12369	第11例
12	吕一	15395006601	340111199007307541	NO.12406	第12例";
        echo "<pre>";

        $rs = explode("\n", $str);
        $data = array();
        foreach ($rs as $value) {
            $array = explode("	", $value);
            $data[] = $array;
        }
        // print_r($data);
        foreach ($data as $key => $value) {
            $data = [
                'fid' => $this->fid,
                'key_1' => $value[1],
                'key_2' => $value[2],
                'key_3' => $value[3],
            ];
            $info = Db::name($this->tableName)->where($data)->find();
            if(!empty($info)){
                if(strpos($info['key_5'], $value[5]) !== false || $info['key_5']==$value[5]) {
                    echo "已存在：".$value[0]."</br>";
                }else{
                    $kee = Db::name($this->tableName)->where('id', $info['id'])->update([
                        'key_5' => $info['key_5'] . '/' . $value[5],
                        'key_4' => $info['key_4'] . '/' . $value[4],
                    ]);
                    echo $value[0]. "更新：".$kee . "</br>";
                }
            }else{
                $data['key_4'] = $value[4];
                $data['key_5'] = $value[5];
                $data['addtime'] = time();
                $kkk = Db::name($this->tableName)->insert($data);
                echo $kkk . "</br>";
            }
        }
        exit();
    }
}
