/* 安徽省非血缘造血干细胞捐献电子荣誉勋章申领系统 - 样式文件 */
html,body{
    margin: 0;
    padding: 0;
}
/* 全局样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}
@font-face {
    font-family: 'NotoSansSChineseLight-Regular';
    src: url('../NotoSansSChineseLight-Regular.otf');
}
.p1{
    color: #cb9254;
    font-family: 'NotoSansSChineseLight-Regular';
    position: absolute;
    bottom: 15vw;
    font-style: italic;
}
.p2{

}
.name{
    color: #cb9254;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-style: normal;
}
.num{
    font-size: 5vw;
    color: #e82329;
    font-family: 'NotoSansSChineseLight-Regular';
    font-style: italic;
}
/* 主展示页面样式 */
.phone-frame {
    width: 375px;
    height: 812px;
    background: #000;
    border-radius: 40px;
    padding: 8px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
    margin: 20px;
}

.phone-screen {
    width: 100%;
    height: 100%;
    background: #fff;
    border-radius: 32px;
    overflow: hidden;
    position: relative;
}

.status-bar {
    height: 44px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    color: white;
    font-size: 14px;
    font-weight: 600;
}

.prototype-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    padding: 20px;
}

.prototype-title {
    width: 100%;
    text-align: center;
    margin-bottom: 30px;
}

iframe {
    border: none;
    width: 100%;
    height: calc(100% - 44px);
}

/* 通用样式 */
.gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 首页样式 */
.form-input {
    transition: all 0.3s ease;
}

.form-input:focus {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.submit-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: all 0.3s ease;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.header-image {
    background: url('https://ztimg.hefei.cc/zt2025/fygxbjxdzxzsq/img/header.jpg');
    background-size: cover;
    background-position: center;
}

/* 成功页样式 */
.certificate-bg {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.medal-glow {
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { 
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.5); 
    }
    to { 
        box-shadow: 0 0 30px rgba(255, 215, 0, 0.8); 
    }
}

/* 头像编辑器样式 */
.avatar-editor-container {
    width: 280px;
    height: 280px;
    margin: 0 auto;
    border: 3px solid #e5e7eb;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.avatar-canvas {
    display: block;
    cursor: pointer;
    touch-action: none;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    width: 100%;
    height: 100%;
    /* 确保Canvas在高DPI屏幕上清晰显示 */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    image-rendering: pixelated;
}

/* 头像框按钮样式 */
.frame-btn {
    transition: all 0.3s ease;
    position: relative;
}

.frame-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

.frame-btn:disabled:hover {
    opacity: 0.4;
    transform: none;
    border-color: #e5e7eb !important;
    box-shadow: none !important;
}

.frame-btn:disabled:hover .frame-preview-img {
    transform: none !important;
}

.frame-btn:disabled:active .frame-preview-img {
    transform: none !important;
}

.frame-btn:disabled.selected .frame-preview-img {
    transform: none !important;
}

.frame-btn:not(:disabled):hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

/* 选中状态的头像框按钮 */
.frame-btn.selected {
    border-color: #3b82f6 !important;
    background-color: #eff6ff !important;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.frame-btn.selected .frame-preview-img {
    transform: scale(1.1);
}

/* 头像框预览图片样式 */
.frame-preview-img {
    transition: transform 0.3s ease;
    border: 1px solid #e5e7eb;
    flex-shrink: 0;
    max-width: none;
}

.frame-btn:hover .frame-preview-img {
    transform: scale(1.05);
}

/* 按钮禁用状态 */
button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

button:disabled:hover {
    opacity: 0.5;
}

/* 模态框样式 */
#resultModal {
    backdrop-filter: blur(4px);
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

#resultModal .bg-white {
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 结果图片样式 */
#resultImage {
    max-width: 220px;
    max-height: 220px;
    border-radius: 12px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    transition: transform 0.15s ease, box-shadow 0.15s ease;
    /* 确保图片在高DPI屏幕上清晰显示 */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    image-rendering: auto;
    /* 防止图片被选中 */
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    /* 禁用拖拽 */
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
}

/* 拖拽时的视觉反馈 */
.avatar-image.dragging {
    cursor: grabbing !important;
}

/* 上传区域悬停效果 */
.avatar-editor-frame:hover .upload-placeholder {
    color: #3b82f6;
}

.avatar-editor-frame:hover .upload-placeholder i {
    transform: scale(1.1);
    transition: transform 0.2s ease;
}

/* 响应式调整 */
@media (max-width: 640px) {
    .avatar-editor-container {
        width: 240px;
        height: 240px;
    }
}

/* 错误页样式 */
.error-animation {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { 
        transform: translateX(0); 
    }
    25% { 
        transform: translateX(-5px); 
    }
    75% { 
        transform: translateX(5px); 
    }
}

.retry-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: all 0.3s ease;
}

.retry-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}



/* 响应式设计 */
@media (max-width: 768px) {
    .phone-frame {
        width: 100%;
        max-width: 375px;
        margin: 10px;
    }
    
    .prototype-container {
        padding: 10px;
    }
}

/* 按钮通用样式 */
button {
    position: relative;
    overflow: hidden;
}

/* 表单验证样式 */
.border-red-500 {
    border-color: #ef4444 !important;
}

.border-green-500 {
    border-color: #10b981 !important;
}

.text-red-500 {
    color: #ef4444;
}

.text-green-500 {
    color: #10b981;
}

/* 页面加载动画 */
.fade-in {
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.fade-in.loaded {
    opacity: 1;
}

/* 波纹效果 */
.ripple {
    position: absolute;
    inset: 0;
    border-radius: inherit;
    background: rgba(255, 255, 255, 0.25);
    transform: scale(0);
    transition: transform 0.3s ease;
}

.ripple.active {
    transform: scale(1);
}



/* 卡片阴影效果 */
.card-shadow {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.card-shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 状态指示器 */
.status-success {
    background-color: #10b981;
    color: white;
}

.status-error {
    background-color: #ef4444;
    color: white;
}

.status-warning {
    background-color: #f59e0b;
    color: white;
}

.status-info {
    background-color: #3b82f6;
    color: white;
}

/* 加载状态 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 工具提示 */
.tooltip {
    position: relative;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.tooltip:hover::after {
    opacity: 1;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 进入动画样式 */
#enterAnimation {
    animation: fadeInBackground 1s ease-in-out;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-size: 400% 400%;
    animation: gradientShift 4s ease infinite, fadeInBackground 1s ease-in-out;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes fadeInBackground {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* 艺术字动画 */
.art-text-container {
    animation: artTextEntrance 1.8s ease-out;
    will-change: transform;
    backface-visibility: hidden;
}



.art-text-image {
    filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.3));
    animation: floatAndGlow 3s ease-in-out infinite 1.8s;
    will-change: transform, filter;
    backface-visibility: hidden;
    transform: translateZ(0);
}



@keyframes artTextEntrance {
    0% {
        transform: scale(0.3) rotate(-360deg);
        opacity: 0;
    }
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

@keyframes floatAndGlow {
    0%, 100% {
        transform: translateY(0px) scale(1);
        filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.3)) brightness(1);
    }
    50% {
        transform: translateY(-10px) scale(1.02);
        filter: drop-shadow(0 15px 30px rgba(0, 0, 0, 0.4)) brightness(1.1);
    }
}

/* 标题文字动画 */
.title-text {
    animation: titleSlideIn 1.5s ease-out 0.5s both;
}

.subtitle-text {
    animation: titleSlideIn 1.5s ease-out 0.8s both;
}

@keyframes titleSlideIn {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 加载指示器 */
.loading-indicator {
    animation: loadingFadeIn 1s ease-out 1.2s both;
}

@keyframes loadingFadeIn {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 加载点动画 */
.loading-dots {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
}

.dot {
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
    animation: dotPulse 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }
.dot:nth-child(3) { animation-delay: 0s; }

@keyframes dotPulse {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}

/* 粒子背景效果 */
.particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    animation: particleFloat 8s linear infinite;
    opacity: 0;
}

.particle:nth-child(1) {
    left: 10%;
    animation-delay: 0s;
    animation-duration: 6s;
}

.particle:nth-child(2) {
    left: 20%;
    animation-delay: 1s;
    animation-duration: 8s;
}

.particle:nth-child(3) {
    left: 30%;
    animation-delay: 2s;
    animation-duration: 7s;
}

.particle:nth-child(4) {
    left: 40%;
    animation-delay: 0.5s;
    animation-duration: 9s;
}

.particle:nth-child(5) {
    left: 60%;
    animation-delay: 1.5s;
    animation-duration: 6.5s;
}

.particle:nth-child(6) {
    left: 70%;
    animation-delay: 2.5s;
    animation-duration: 8.5s;
}

.particle:nth-child(7) {
    left: 80%;
    animation-delay: 3s;
    animation-duration: 7.5s;
}

.particle:nth-child(8) {
    left: 90%;
    animation-delay: 3.5s;
    animation-duration: 9.5s;
}

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* 主要内容初始隐藏 */
.main-content {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.8s ease-out;
}

.main-content.show {
    opacity: 1;
    transform: translateY(0);
}

/* 进入动画退出效果 */
#enterAnimation.fade-out {
    animation: fadeOutAnimation 0.8s ease-in-out forwards;
}

@keyframes fadeOutAnimation {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    100% {
        opacity: 0;
        transform: scale(1.1);
    }
}

body{
    -webkit-text-size-adjust: 100% !important;
}

/* 浮动按钮样式 */
.floating-avatar-btn {
    position: fixed;
    bottom: 30px;
    right: 20px;
    z-index: 10;
    animation: floatIn 0.8s ease-out;
}

.avatar-floating-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 50px;
    padding: 12px 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    min-width: 140px;
    justify-content: center;
}

.avatar-floating-button:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.avatar-floating-button:active {
    transform: translateY(-1px) scale(1.02);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5);
}

.avatar-btn-icon {
    font-size: 18px;
    animation: pulse 2s infinite;
}

.avatar-btn-text {
    font-size: 13px;
    white-space: nowrap;
}

/* 浮动按钮进入动画 */
@keyframes floatIn {
    0% {
        transform: translateY(100px) scale(0.8);
        opacity: 0;
    }
    100% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

/* 图标脉冲动画 */
@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* 响应式调整 */
@media (max-width: 480px) {
    .floating-avatar-btn {
        bottom: 20px;
        right: 15px;
    }

    .avatar-floating-button {
        padding: 10px 16px;
        min-width: 120px;
        font-size: 13px;
    }

    .avatar-btn-icon {
        font-size: 16px;
    }

    .avatar-btn-text {
        font-size: 12px;
    }
}