<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0,minimal-ui:ios" name="viewport">
    <title>{$zt_name}</title>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/tailwindcss.js"></script>
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/fontawesome-free-5.15.4-web/css/all.min.css">
    <link rel="stylesheet" href="css/index.css?v={php} echo mt_rand(1000,999999);{/php}">
</head>
<body class="bg-gray-50">

    <div id="form-container">
        <!-- 题图区域 -->
        <div class="header-image h-48 flex items-center justify-center text-white">
            <!-- <div class="text-center">
                <i class="fas fa-medal text-6xl mb-4 text-yellow-300"></i>
                <h1 class="text-2xl font-bold mb-2">填写捐献信息领取勋章</h1>
                <p class="text-sm opacity-90">感谢您献出的爱心</p>
            </div> -->
        </div>

        <!-- 主要内容区域 -->
        <div class="px-6 py-6">
            <!-- 说明文字 -->
            <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
                <div class="flex items-center space-x-3 mb-6">
                    <i class="fas fa-info-circle text-blue-500 text-xl"></i>
                    <h3 class="text-lg font-semibold text-gray-800">申领说明</h3>
                </div>
                <p class="text-gray-600 leading-relaxed">
                    感谢您献出的爱心，请在下方信息表单中填写您的捐献信息并提交，后台核对信息后，即可领取您的电子荣誉纪念章。
                </p>
            </div>

            <!-- 信息录入表单 -->
            <div class="bg-white rounded-2xl p-6 shadow-lg">
                <h2 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
                    <i class="fas fa-edit text-blue-500 mr-3"></i>
                    捐献信息填写
                </h2>
                
                <form class="space-y-5">
                    <!-- 姓名 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-user mr-2 text-gray-500"></i>姓名
                        </label>
                        <input type="text"
                               name="name"
                               class="form-input w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                               placeholder="请输入您的真实姓名"
                               required>
                    </div>

                    <!-- 身份证号 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-id-card mr-2 text-gray-500"></i>身份证号
                        </label>
                        <input type="text"
                               name="id_card"
                               class="form-input w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                               placeholder="请输入18位身份证号码"
                               pattern="[0-9X]{18}"
                               required>
                    </div>

                    <!-- 捐献证书号 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-certificate mr-2 text-gray-500"></i>全国捐献编号
                        </label>
                        <div class="relative w-full border border-gray-200 rounded-xl focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-transparent">
                            <span class="absolute left-4 top-6 transform -translate-y-1/2 text-red-500 font-bold text-base pointer-events-none z-50">NO. </span>
                            <input type="number"
                                   name="certificate_no"
                                   class="form-input w-full pl-16 pr-4 py-3 border-0 rounded-xl focus:ring-0 focus:border-transparent outline-none"
                                   placeholder="仅填写数字即可"
                                   required>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <button type="submit" 
                            class="submit-btn w-full py-4 text-white font-semibold rounded-xl flex items-center justify-center space-x-2 mt-8">
                        <i class="fas fa-medal"></i>
                        <span>领取纪念章</span>
                    </button>
                </form>
            </div>

            <!-- 底部提示 -->
            <div class="mt-6 text-center">
                <p class="text-xs text-gray-500">
                    <i class="fas fa-shield-alt mr-1"></i>
                    您的个人信息将被严格保护
                </p>
            </div>
        </div>
    </div>

    <div id="error-container" style="display: none;">
        <!-- 主要内容区域 -->
        <div class="px-6 py-8 pt-12">
            <!-- 错误提示 -->
            <div class="text-center mb-8">
                <div class="error-animation inline-block bg-red-100 rounded-full p-6 mb-4">
                    <i class="fas fa-exclamation-triangle text-6xl text-red-500"></i>
                </div>
                <h2 class="text-2xl font-bold text-gray-800 mb-2">信息验证失败</h2>
                <p class="text-gray-600">很抱歉，系统未识别到您的捐赠信息</p>
            </div>

            <!-- 详细说明 -->
            <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
                <div class="flex items-center space-x-3 mb-6">
                    <i class="fas fa-info-circle text-red-500 text-xl"></i>
                    <h3 class="text-lg font-semibold text-gray-800">可能的原因</h3>
                </div>
                <ul class="text-gray-700 space-y-4">
                    <li class="flex items-center space-x-3">
                        <span class="bg-red-100 text-red-600 rounded-full w-6 h-6 flex items-center justify-center text-sm font-semibold shrink-0">1</span>
                        <span>姓名、手机号、身份证号或捐献证书号填写有误</span>
                    </li>
                    <li class="flex items-center space-x-3">
                        <span class="bg-red-100 text-red-600 rounded-full w-6 h-6 flex items-center justify-center text-sm font-semibold shrink-0">2</span>
                        <span>系统数据同步延迟</span>
                    </li>
                </ul>
            </div>

            <!-- 解决方案 -->
            <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
                <h3 class="font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-lightbulb text-yellow-500 mr-3"></i>
                    解决方案
                </h3>
                <div class="space-y-4">
                    <div class="bg-blue-50 rounded-xl p-4">
                        <h4 class="font-medium text-blue-800 mb-2">1. 重新核对信息</h4>
                        <p class="text-sm text-blue-700">
                            请仔细检查您填写的姓名、手机号、身份证号和捐献证书号是否准确无误，确保与您的捐献记录完全一致。
                        </p>
                    </div>
                    <div class="bg-green-50 rounded-xl p-4">
                        <h4 class="font-medium text-green-800 mb-2">2. 联系工作人员</h4>
                        <p class="text-sm text-green-700">
                            如果多次尝试仍无法识别，请联系安徽省红十字会工作人员。
                        </p>
                    </div>
                </div>
            </div>

            <!-- 联系方式 -->
            <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
                <h3 class="font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-headset text-blue-600 mr-3"></i>
                    联系我们
                </h3>

                <!-- 机构名称 -->
                <div class="flex items-center space-x-3 mb-4">
                    <i class="fas fa-building text-gray-600"></i>
                    <span class="font-medium text-gray-800">安徽省红十字会</span>
                </div>

                <!-- 联系电话 -->
                <div class="mb-4">
                    <div class="flex items-center space-x-3 mb-2">
                        <i class="fas fa-phone text-green-600"></i>
                        <span class="text-gray-700">联系电话</span>
                    </div>
                    <a href="tel:400-1234-5678" class="pl-6">
                        <span class="text-lg font-semibold text-gray-800">400-1234-5678</span>
                    </a>
                </div>

                <!-- 服务时间 -->
                <div>
                    <div class="flex items-center space-x-3 mb-2">
                        <i class="fas fa-clock text-blue-600"></i>
                        <span class="text-gray-700">服务时间</span>
                    </div>
                    <div class="pl-6">
                        <span class="text-gray-600">工作日 9:00-17:00</span>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="space-y-4">
                <a href="#" id="retry-btn" class="retry-btn w-full py-4 text-white font-semibold rounded-xl flex items-center justify-center space-x-2">
                    <i class="fas fa-redo-alt"></i>
                    <span>重新填写信息</span>
                </a>
            </div>

            <!-- 温馨提示 -->
            <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-xl p-4">
                <div class="flex items-start space-x-3">
                    <i class="fas fa-heart text-yellow-600 text-lg mt-1"></i>
                    <div>
                        <h4 class="font-medium text-yellow-800 mb-1">温馨提示</h4>
                        <p class="text-sm text-yellow-700">
                            您的爱心捐献是珍贵的生命礼物，我们会尽快为您解决问题。感谢您的耐心与理解！
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 浮动按钮 - 生成荣誉头像 -->
    <div class="floating-avatar-btn">
        <button id="avatarFloatingBtn" class="avatar-floating-button">
            <div class="avatar-btn-icon">
                <i class="fas fa-user-circle"></i>
            </div>
            <span class="avatar-btn-text">生成荣誉头像</span>
        </button>
    </div>
    <script>
        // 将startData设置为全局变量，供app.js使用
        window.startData = {
            name:'{$name}',
            phone:'{$phone}',
            id_card:'{$id_card}',
            certificate_no:'{$certificate_no}',
            rank:'{$rank}'
        };
    </script>
    <script src="https://z.hfurl.cc/static/common/js/hook/hook1.js"></script>
    <script src="js/app.js?v={php} echo mt_rand(1000,999999);{/php}"></script>
    <!--分享-->
    {include file="share"/}
</body>
</html>