<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$zt_name}</title>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/tailwindcss.js"></script>
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/fontawesome-free-5.15.4-web/css/all.min.css">
    <link rel="stylesheet" href="css/index.css?v={php} echo mt_rand(1000,999999);{/php}">
</head>
<body class="bg-gray-100">
    <div class="prototype-title">
        <div class="bg-white rounded-2xl shadow-lg p-8 mb-8 mx-4">
            <div class="flex items-center justify-center mb-4">
                <i class="fas fa-medal text-6xl text-yellow-500 mr-4"></i>
                <div>
                    <h1 class="text-3xl font-bold text-gray-800 mb-2">安徽省非血缘造血干细胞捐献</h1>
                    <h2 class="text-2xl font-semibold text-blue-600">电子荣誉勋章申领系统</h2>
                </div>
            </div>
            <p class="text-gray-600 text-center">高保真移动端原型展示 - iPhone 15 Pro 尺寸</p>
            <div class="flex justify-center space-x-6 mt-4 text-sm text-gray-500">
                <span><i class="fas fa-mobile-alt mr-2"></i>375 × 812</span>
                <span><i class="fas fa-palette mr-2"></i>Tailwind CSS</span>
                <span><i class="fas fa-icons mr-2"></i>FontAwesome</span>
            </div>
        </div>
    </div>
    
    <div class="prototype-container">
        <!-- 首页信息录入 -->
        <div class="flex flex-col items-center">
            <div class="text-center mb-4">
                <h3 class="text-lg font-semibold text-gray-800 mb-2">首页 - 信息录入</h3>
                <div class="flex justify-center space-x-2 mt-2">
                    <span class="bg-blue-100 text-blue-600 px-2 py-1 rounded text-xs">表单验证</span>
                    <span class="bg-green-100 text-green-600 px-2 py-1 rounded text-xs">响应式设计</span>
                </div>
            </div>
            <div class="phone-frame">
                <div class="phone-screen">
                    <div class="status-bar">
                        <div class="flex items-center space-x-1">
                            <span>9:41</span>
                        </div>
                        <div class="flex items-center space-x-1">
                            <i class="fas fa-signal text-sm"></i>
                            <i class="fas fa-wifi text-sm"></i>
                            <i class="fas fa-battery-three-quarters text-sm"></i>
                        </div>
                    </div>
                    <iframe src="home?v={php} echo mt_rand(1000,999999);{/php}"></iframe>
                </div>
            </div>
        </div>

        <!-- 成功页面 -->
        <div class="flex flex-col items-center">
            <div class="text-center mb-4">
                <h3 class="text-lg font-semibold text-gray-800 mb-2">成功页 - 勋章生成</h3>
                <div class="flex justify-center space-x-2 mt-2">
                    <span class="bg-yellow-100 text-yellow-600 px-2 py-1 rounded text-xs">证书生成</span>
                    <span class="bg-purple-100 text-purple-600 px-2 py-1 rounded text-xs">头像编辑</span>
                </div>
            </div>
            <div class="phone-frame">
                <div class="phone-screen">
                    <div class="status-bar">
                        <div class="flex items-center space-x-1">
                            <span>9:41</span>
                        </div>
                        <div class="flex items-center space-x-1">
                            <i class="fas fa-signal text-sm"></i>
                            <i class="fas fa-wifi text-sm"></i>
                            <i class="fas fa-battery-three-quarters text-sm"></i>
                        </div>
                    </div>
                    <iframe src="cg?v={php} echo mt_rand(1000,999999);{/php}"></iframe>
                </div>
            </div>
        </div>

        <!-- 错误提示页面 -->
        <div class="flex flex-col items-center">
            <div class="text-center mb-4">
                <h3 class="text-lg font-semibold text-gray-800 mb-2">错误页 - 信息验证失败</h3>
                <div class="flex justify-center space-x-2 mt-2">
                    <span class="bg-red-100 text-red-600 px-2 py-1 rounded text-xs">错误处理</span>
                    <span class="bg-orange-100 text-orange-600 px-2 py-1 rounded text-xs">用户引导</span>
                </div>
            </div>
            <div class="phone-frame">
                <div class="phone-screen">
                    <div class="status-bar">
                        <div class="flex items-center space-x-1">
                            <span>9:41</span>
                        </div>
                        <div class="flex items-center space-x-1">
                            <i class="fas fa-signal text-sm"></i>
                            <i class="fas fa-wifi text-sm"></i>
                            <i class="fas fa-battery-three-quarters text-sm"></i>
                        </div>
                    </div>
                    <iframe src="cw?v={php} echo mt_rand(1000,999999);{/php}"></iframe>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
