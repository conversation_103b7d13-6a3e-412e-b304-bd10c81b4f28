// 安徽省非血缘造血干细胞捐献电子荣誉勋章申领系统
// 交互逻辑脚本

document.addEventListener('DOMContentLoaded', function() {
    
    // 表单验证逻辑
    function initFormValidation() {
        const form = document.querySelector('form');
        if (!form) return;

        const inputs = form.querySelectorAll('input[required]');
        const submitBtn = form.querySelector('button[type="submit"]');

        // 实时验证
        inputs.forEach(input => {
            input.addEventListener('input', function() {
                validateField(this);
                updateSubmitButton();
            });

            input.addEventListener('blur', function() {
                validateField(this);
            });

            // 初始化时检查已有值（处理预填充数据）
            if (input.value.trim()) {
                validateField(input);
            }
        });

        // 初始化后更新提交按钮状态
        updateSubmitButton();

        // 表单提交
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            let isValid = true;
            inputs.forEach(input => {
                if (!validateField(input)) {
                    isValid = false;
                }
            });

            if (isValid) {
                simulateFormSubmission();
            }
        });

        function getLabelText(field) {
            // Find the container div that holds both the label and the input
            let container = field.parentNode;
            // For nested inputs, go up until the div with the label is found
            while (container && !container.querySelector('label')) {
                container = container.parentNode;
            }

            if (container) {
                const label = container.querySelector('label');
                if (label) {
                    // Clone the label node to avoid modifying the original
                    const labelClone = label.cloneNode(true);
                    // Remove the icon from the clone to get only the text
                    const icon = labelClone.querySelector('i');
                    if (icon) {
                        icon.remove();
                    }
                    return labelClone.textContent.trim();
                }
            }
            // Fallback to the field's name or placeholder if a label is not found
            return field.name || field.placeholder || '此字段';
        }

        function validateField(field) {
            const value = field.value.trim();
            const fieldName = getLabelText(field);
            
            // 移除之前的错误样式
            field.classList.remove('border-red-500', 'border-green-500');
            
            let isValid = true;
            let errorMessage = '';

            // 基础验证
            if (!value) {
                isValid = false;
                errorMessage = `${fieldName}不能为空`;
            } else {
                // 特定字段验证 (using field.name for robustness)
                switch(field.name) {
                    case 'id_card':
                        if (!/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(value)) {
                            isValid = false;
                            errorMessage = '请输入正确的身份证号码';
                        }
                        break;
                    case 'name':
                        if (value.length < 2 || value.length > 10) {
                            isValid = false;
                            errorMessage = '姓名长度应在2-10个字符之间';
                        }
                        break;
                    // No specific validation for certificate_no, just the required check
                }
            }

            // 应用验证结果样式
            if (isValid) {
                field.classList.add('border-green-500');
                removeErrorMessage(field);
            } else {
                field.classList.add('border-red-500');
                showErrorMessage(field, errorMessage);
            }

            return isValid;
        }

        function showErrorMessage(field, message) {
            removeErrorMessage(field);
            const errorDiv = document.createElement('div');
            errorDiv.className = 'text-red-500 text-xs mt-1 error-message';
            errorDiv.textContent = message;
            
            // 找到正确的容器来放置错误消息
            const container = findErrorContainer(field);
            container.appendChild(errorDiv);
        }

        function removeErrorMessage(field) {
            const container = findErrorContainer(field);
            const existingError = container.querySelector('.error-message');
            if (existingError) {
                existingError.remove();
            }
        }

        function findErrorContainer(field) {
            // 对于捐献证书号字段，需要找到最外层的div容器
            if (field.name === 'certificate_no') {
                // 向上查找到包含label的div容器
                let parent = field.parentNode;
                while (parent && !parent.querySelector('label')) {
                    parent = parent.parentNode;
                }
                return parent || field.parentNode;
            }
            // 其他字段直接使用父节点
            return field.parentNode;
        }

        function updateSubmitButton() {
            const allValid = Array.from(inputs).every(input => 
                input.classList.contains('border-green-500')
            );
            
            if (submitBtn) {
                if (allValid) {
                    submitBtn.disabled = false;
                    submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
                } else {
                    submitBtn.disabled = true;
                    submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
                }
            }
        }

        function simulateFormSubmission() {
            if (submitBtn) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>提交中...';
                submitBtn.disabled = true;

                // 收集表单数据
                const formData = new FormData();
                const nameInput = form.querySelector('input[name="name"]');
                // const phoneInput = form.querySelector('input[name="phone"]');
                const idCardInput = form.querySelector('input[name="id_card"]');
                const certificateInput = form.querySelector('input[name="certificate_no"]');

                if (nameInput) formData.append('name', nameInput.value.trim());
                // if (phoneInput) formData.append('phone', phoneInput.value.trim());
                if (idCardInput) formData.append('id_card', idCardInput.value.trim());
                if (certificateInput) formData.append('certificate_no', certificateInput.value.trim());

                // API端点配置（可根据环境修改）
                const apiEndpoint = 'submit';

                // 发起fetch请求
                defaultFetch(apiEndpoint,{
                    name:nameInput.value.trim(),
                    // phone:phoneInput.value.trim(),
                    id_card:idCardInput.value.trim(),
                    certificate_no:certificateInput.value.trim()
                }).then(data => {
                    if (data.status===1) {
                        window.location.href = 'cg';
                    } else {
                        const formContainer = document.getElementById('form-container');
                        const errorContainer = document.getElementById('error-container');
                        if (formContainer) formContainer.style.display = 'none';
                        if (errorContainer) errorContainer.style.display = 'block';
                        // 切换到页面顶部
                        window.scrollTo(0, 0);
                    }
                }).catch(error => {
                    showSubmissionError('网络错误，请检查网络连接后重试');
                }).finally(() => {
                    // 恢复按钮状态
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                });
            }
        }

        function showSubmissionError(message) {
            // 创建错误提示
            const errorDiv = document.createElement('div');
            errorDiv.className = 'bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl mt-4 flex items-center';
            errorDiv.innerHTML = `
                <i class="fas fa-exclamation-triangle mr-2"></i>
                <span>${message}</span>
            `;

            // 移除之前的错误提示
            const existingError = form.querySelector('.bg-red-50');
            if (existingError) {
                existingError.remove();
            }

            // 在表单后添加错误提示
            form.appendChild(errorDiv);

            // 3秒后自动移除错误提示
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.remove();
                }
            }, 3000);
        }
    }

    // 头像编辑功能
    function initAvatarEditor() {
        const fileInput = document.getElementById('fileInput');
        const uploadBtn = document.getElementById('uploadBtn');
        const wechatBtn = document.getElementById('wechatBtn');
        const generateBtn = document.getElementById('generateBtn');
        const resultModal = document.getElementById('resultModal');
        const resultImage = document.getElementById('resultImage');
        const closeModalBtn = document.getElementById('closeModalBtn');

        // 头像框按钮 - 自动检测所有头像框按钮
        const frameButtons = document.querySelectorAll('[id^="frameBtn"]');

        if (!fileInput || !uploadBtn || !wechatBtn || !generateBtn) return;

        // 创建Canvas编辑器
        const editor = new AvatarEditor('avatarCanvas');

        // 上传功能
        uploadBtn.addEventListener('click', () => {
            fileInput.click();
        });



        // 文件选择处理
        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // 加载新图片时自动重置
                    editor.reset();
                    editor.loadImage(e.target.result);

                    // 启用头像框按钮和生成按钮
                    frameButtons.forEach(btn => {
                        if (btn) btn.disabled = false;
                    });
                    generateBtn.disabled = false;
                };
                reader.readAsDataURL(file);
            }
        });

        // 微信头像功能
        wechatBtn.addEventListener('click', () => {
            loadWechatAvatar();
        });

        // 头像框选择功能
        frameButtons.forEach((btn) => {
            if (btn) {
                btn.addEventListener('click', () => {
                    // 移除所有按钮的选中状态
                    frameButtons.forEach(b => {
                        if (b) b.classList.remove('selected');
                    });

                    // 添加当前按钮的选中状态
                    btn.classList.add('selected');

                    // 从按钮ID中提取数字，确保调用正确的头像框
                    const frameNumber = parseInt(btn.id.replace('frameBtn', ''));
                    editor.setFrame(frameNumber);
                });
            }
        });



        // 生成荣誉头像
        generateBtn.addEventListener('click', () => {
            generateHonorAvatar();
        });

        function generateHonorAvatar() {
            // 显示加载状态
            const originalText = generateBtn.innerHTML;
            generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin text-lg mb-1"></i><span class="text-sm">生成中...</span>';
            generateBtn.disabled = true;

            // 模拟生成过程（添加一点延迟让用户看到加载状态）
            setTimeout(() => {
                try {
                    // 获取编辑器的高质量图像数据
                    const imageData = editor.getImageData(1.0); // 最高质量

                    // 直接将图像数据设置给img元素
                    resultImage.src = imageData;

                    // 确保图片加载完成后显示模态框
                    resultImage.onload = function() {
                        // 恢复按钮状态
                        generateBtn.innerHTML = originalText;
                        generateBtn.disabled = false;

                        // 设置模态框标题
                        const modalTitle = document.getElementById('resultModalTitle');
                        if (modalTitle) {
                        modalTitle.innerHTML = '<i class="fas fa-check-circle text-green-500 mr-2"></i>荣誉头像生成成功';
                    }

                    // 显示结果模态框
                    resultModal.style.display = 'flex';
                };

                // 如果图片加载失败
                resultImage.onerror = function() {
                    alert('图片生成失败，请重试');
                    generateBtn.innerHTML = originalText;
                    generateBtn.disabled = false;
                };
                } catch (error) {
                    // 处理Canvas导出错误
                    console.error('生成荣誉头像时出错:', error);
                    alert(error.message || '图片生成失败，可能是由于跨域限制。请重试或联系管理员。');
                    generateBtn.innerHTML = originalText;
                    generateBtn.disabled = false;
                }
            }, 500); // 500ms延迟，让用户看到生成过程
        }

        // 模态框控制
        closeModalBtn.addEventListener('click', () => {
            resultModal.style.display = 'none';
        });

        // 点击模态框背景关闭
        resultModal.addEventListener('click', (e) => {
            if (e.target === resultModal) {
                resultModal.style.display = 'none';
            }
        });

        // 加载微信头像功能
        function loadWechatAvatar() {
            // 检查avatar变量是否存在且有效
            if (typeof avatar === 'undefined' || !avatar || avatar === '{$avatar}') {
                alert('未获取到微信头像，请使用"选择图片"功能上传头像');
                return;
            }

            // 显示加载状态
            const originalText = wechatBtn.innerHTML;
            wechatBtn.innerHTML = '<i class="fas fa-spinner fa-spin text-lg mb-1"></i><span class="text-sm">加载中...</span>';
            wechatBtn.disabled = true;

            // 加载微信头像
            try {
                // 重置编辑器
                editor.reset();

                // 加载avatar图片
                editor.loadImage(avatar);

                // 启用头像框按钮和生成按钮
                frameButtons.forEach(btn => {
                    if (btn) btn.disabled = false;
                });
                generateBtn.disabled = false;

                // 恢复按钮状态
                wechatBtn.innerHTML = originalText;
                wechatBtn.disabled = false;

            } catch (error) {
                console.error('加载微信头像失败:', error);
                alert('加载微信头像失败，请重试或使用"选择图片"功能');

                // 恢复按钮状态
                wechatBtn.innerHTML = originalText;
                wechatBtn.disabled = false;
            }
        }
    }

    // 证书生成功能
    function initCertificateGenerator() {
        const generateCertificateBtn = document.getElementById('generateCertificateBtn');
        const resultModal = document.getElementById('resultModal');
        const resultImage = document.getElementById('resultImage');

        if (!generateCertificateBtn || !resultModal || !resultImage) return;

        // 生成证书按钮点击事件
        generateCertificateBtn.addEventListener('click', () => {
            generateCertificate();
        });

        function generateCertificate() {
            // 显示加载状态
            const originalText = generateCertificateBtn.innerHTML;
            generateCertificateBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>生成中...';
            generateCertificateBtn.disabled = true;

            // 模拟生成过程
            setTimeout(() => {
                // 获取poster_area元素
                const posterArea = document.querySelector('.poster_area');
                if (!posterArea) {
                    alert('未找到证书内容区域');
                    generateCertificateBtn.innerHTML = originalText;
                    generateCertificateBtn.disabled = false;
                    return;
                }

                // 使用html2canvas生成图片
                html2canvas(posterArea, {
                    useCORS: true,
                    allowTaint: true,
                    scale: 2, // 提高图片质量
                    backgroundColor: null
                }).then(canvas => {
                    // 将canvas转换为图片数据
                    const imageData = canvas.toDataURL('image/png', 1.0);

                    // 设置结果图片
                    resultImage.src = imageData;

                    // 确保图片加载完成后显示模态框
                    resultImage.onload = function() {
                        // 恢复按钮状态
                        generateCertificateBtn.innerHTML = originalText;
                        generateCertificateBtn.disabled = false;

                        // 设置模态框标题
                        const modalTitle = document.getElementById('resultModalTitle');
                        if (modalTitle) {
                            modalTitle.innerHTML = '<i class="fas fa-check-circle text-green-500 mr-2"></i>荣誉证书生成成功';
                        }

                        // 显示结果模态框
                        resultModal.style.display = 'flex';
                    };

                    // 如果图片加载失败
                    resultImage.onerror = function() {
                        alert('证书生成失败，请重试');
                        generateCertificateBtn.innerHTML = originalText;
                        generateCertificateBtn.disabled = false;
                    };
                }).catch(error => {
                    console.error('证书生成失败:', error);
                    alert('证书生成失败，请重试');
                    generateCertificateBtn.innerHTML = originalText;
                    generateCertificateBtn.disabled = false;
                });
            }, 500); // 500ms延迟，让用户看到生成过程
        }
    }

    // 按钮点击效果
    function initButtonEffects() {
        const buttons = document.querySelectorAll('button');
        buttons.forEach(btn => {
            btn.addEventListener('click', function() {
                // 添加点击波纹效果
                const ripple = document.createElement('span');
                ripple.className = 'absolute inset-0 rounded-xl bg-white opacity-25 transform scale-0 transition-transform duration-300';
                
                if (this.style.position !== 'relative') {
                    this.style.position = 'relative';
                }
                
                this.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.style.transform = 'scale-100';
                }, 10);
                
                setTimeout(() => {
                    ripple.remove();
                }, 300);
            });
        });
    }

    // 设置海报内容
    function initPosterContent(){
        
        // 生成并渲染整个p1结构
        const p1Container = document.getElementById('p1Container');
        if (p1Container) {
            const p1Html = generateP1Html(startData);
            p1Container.innerHTML = p1Html;
        }
        
        // 生成完整的p1 HTML结构
        function generateP1Html(data) {
            const name = data.name || '捐献者';
            const rankText = generateRankText(data.rank);

            return `
                我是<span class="name px-1">${name}</span>
                <p class="p2 text-xs inline-block">${rankText}</p><br>
                跟我一起，让爱与希望传递!
            `;
        }

        // 根据rank数据生成排名文本
        function generateRankText(rank) {
            if (!rank) return '安徽省捐献者';

            // 移除可能的"第"和"例"字符，提取数字
            const numbers = rank.match(/\d+/g);

            if (!numbers || numbers.length === 0) {
                return '安徽省捐献者';
            }

            if (numbers.length === 1) {
                // 单个排名：如"第30例"
                return `安徽省第<span class="num">${numbers[0]}</span>例捐献者`;
            } else {
                // 多个排名：如"第30例、第272例"
                const rankSpans = numbers.map(num => `第<span class="num">${num}</span>例`).join('、');
                return `安徽省${rankSpans}捐献者`;
            }
        }

    }

    // 首页数据处理功能
    function initHomePageLogic() {
        // 检查是否存在startData（从全局变量获取）
        const nameInput = document.querySelector('input[name="name"]');
        if (typeof window.startData !== 'undefined' && window.startData.name && nameInput) {
            showExistingDataDialog();
        }
    }

    // 浮动按钮功能
    function initFloatingButton() {
        const avatarFloatingBtn = document.getElementById('avatarFloatingBtn');
        if (!avatarFloatingBtn) return;

        // 添加点击事件
        avatarFloatingBtn.addEventListener('click', function() {
            // 添加点击动画效果
            this.style.transform = 'translateY(-1px) scale(0.98)';

            setTimeout(() => {
                this.style.transform = '';
                // 跳转到avatar页面
                window.location.href = 'avatar';
            }, 150);
        });

        // 添加触摸反馈（移动端）
        avatarFloatingBtn.addEventListener('touchstart', function() {
            this.style.transform = 'translateY(-1px) scale(0.98)';
        });

        avatarFloatingBtn.addEventListener('touchend', function() {
            this.style.transform = '';
        });
    }

    // 显示已绑定信息的确认对话框
    function showExistingDataDialog() {
        // 创建模态框HTML
        const modalHTML = `
            <div id="existingDataModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: flex;">
                <div class="bg-white rounded-2xl p-6 mx-4 max-w-sm w-full shadow-2xl">
                    <div class="text-center">
                        <div class="mb-4">
                            <i class="fas fa-info-circle text-blue-500 text-4xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-3">系统提示</h3>
                        <p class="text-gray-600 mb-6 leading-relaxed">
                            系统已检测到您已绑定过捐献信息，是否立即查看证书？
                        </p>
                        <div class="flex space-x-3">
                            <button id="cancelViewBtn" class="flex-1 py-3 px-4 bg-gray-100 text-gray-700 rounded-xl font-medium hover:bg-gray-200 transition-colors">
                                取消
                            </button>
                            <button id="viewCertificateBtn" class="flex-1 py-3 px-4 bg-blue-500 text-white rounded-xl font-medium hover:bg-blue-600 transition-colors">
                                查看证书
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 将模态框添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // 绑定事件
        const modal = document.getElementById('existingDataModal');
        const cancelBtn = document.getElementById('cancelViewBtn');
        const viewBtn = document.getElementById('viewCertificateBtn');

        // 取消按钮 - 关闭模态框，继续填写表单
        cancelBtn.addEventListener('click', function() {
            modal.remove();
            // 可以选择性地预填充表单
            // prefillForm();
        });

        // 查看证书按钮 - 跳转到证书页面
        viewBtn.addEventListener('click', function() {
            // 这里可以根据实际需求跳转到证书页面
            window.location.href = 'cg';
        });
    }

    // 预填充表单数据
    function prefillForm() {
        // 检查startData是否存在
        if (typeof window.startData === 'undefined') {
            console.warn('startData not found');
            return;
        }

        const nameInput = document.querySelector('input[name="name"]');
        // const phoneInput = document.querySelector('input[name="phone"]');
        const idCardInput = document.querySelector('input[name="id_card"]');
        const certificateInput = document.querySelector('input[name="certificate_no"]');

        // 设置值
        if (nameInput) nameInput.value = window.startData.name || '';
        // if (phoneInput) phoneInput.value = window.startData.phone || '';
        if (idCardInput) idCardInput.value = window.startData.id_card || '';
        if (certificateInput) certificateInput.value = window.startData.certificate_no || '';

        // 手动触发input事件以激活验证
        const inputs = [nameInput, idCardInput, certificateInput];
        inputs.forEach(input => {
            if (input && input.value) {
                // 创建并触发input事件
                const event = new Event('input', { bubbles: true });
                input.dispatchEvent(event);
            }
        });
    }

    function initRetryButton() {
        const retryBtn = document.getElementById('retry-btn');
        const formContainer = document.getElementById('form-container');
        const errorContainer = document.getElementById('error-container');

        if (retryBtn && formContainer && errorContainer) {
            retryBtn.addEventListener('click', function(e) {
                e.preventDefault();
                errorContainer.style.display = 'none';
                formContainer.style.display = 'block';
                window.scrollTo(0, 0);
            });
        }
    }

    // 初始化所有功能
    initFormValidation();
    initAvatarEditor();
    initCertificateGenerator();
    initButtonEffects();
    initPosterContent();
    initHomePageLogic(); // 添加首页逻辑初始化
    initFloatingButton(); // 添加浮动按钮初始化
    initRetryButton();

    // 页面加载动画
    document.body.style.opacity = '0';
    setTimeout(() => {
        document.body.style.transition = 'opacity 0.5s ease-in-out';
        document.body.style.opacity = '1';
    }, 100);
});
(function() {
    if (typeof WeixinJSBridge == "object" && typeof WeixinJSBridge.invoke == "function") {
        handleFontSize();
    } else {
        if (document.addEventListener) {
            document.addEventListener("WeixinJSBridgeReady", handleFontSize, false);
        } else if (document.attachEvent) {
            document.attachEvent("WeixinJSBridgeReady", handleFontSize);
            document.attachEvent("onWeixinJSBridgeReady", handleFontSize);
        }
    }
    function handleFontSize() {
        console.log(124);
        
        WeixinJSBridge.invoke('setFontSizeCallback', { 'fontSize' : 0 });
        WeixinJSBridge.on('menu:setfont', function() {
            WeixinJSBridge.invoke('setFontSizeCallback', { 'fontSize' : 0 });
        });
    }
})();