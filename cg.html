<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0,minimal-ui:ios" name="viewport">
    <title>{$zt_name}</title>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/tailwindcss.js"></script>
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/fontawesome-free-5.15.4-web/css/all.min.css">
    <link rel="stylesheet" href="css/index.css?v={php} echo mt_rand(1000,999999);{/php}">
</head>
<body class="bg-gray-50">
    <!-- 进入动画遮罩层 -->
    <div id="enterAnimation" class="fixed inset-0 z-50 bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 flex items-center justify-center">
        <!-- 动画容器 -->
        <div class="relative flex flex-col items-center justify-center">
            <!-- 800艺术字图片 -->
            <div class="art-text-container mb-8">
                <img src="https://ztimg.hefei.cc/zt2025/fygxbjxdzxzsq/img/800.png?v=1" alt="800" class="art-text-image" style="width: 250px; height: auto;">
            </div>

            <!-- 标题文字 -->
            <div class="text-center text-white mb-8">
                <h1 class="title-text text-2xl font-bold mb-2">安徽省非血缘造血干细胞捐献</h1>
                <h2 class="subtitle-text text-lg">电子荣誉勋章申领系统</h2>
            </div>

            <!-- 加载指示器 -->
            <div class="loading-indicator">
                <div class="loading-dots">
                    <div class="dot"></div>
                    <div class="dot"></div>
                    <div class="dot"></div>
                </div>
                <p class="loading-text text-white text-sm mt-4">正在加载...</p>
            </div>
        </div>

        <!-- 粒子背景效果 -->
        <div class="particles-container">
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
        </div>
    </div>

    <!-- 主要内容容器 -->
    <div id="mainContent" class="main-content">
        <!-- 成功提示 -->
    <div class="px-6 py-4 pt-8">
        <div class="bg-green-50 border border-green-200 rounded-2xl p-4 flex items-center space-x-3">
            <i class="fas fa-check-circle text-green-500 text-2xl"></i>
            <div>
                <h3 class="font-semibold text-green-800">验证成功！</h3>
                <p class="text-sm text-green-600">您的捐献信息已通过验证</p>
            </div>
        </div>
    </div>

    <!-- 电子证书 -->
    <div class="px-6 mb-6">
        <div class="bg-white rounded-2xl p-6 shadow-lg">
            <h3 class="text-xl font-bold text-gray-800 mb-2 flex items-center">
                <i class="fas fa-user-edit text-blue-500 mr-3"></i>
                荣誉证书
            </h3>
            <div class="text-center">
                <div class="poster_area flex flex-col items-center relative mb-4">
                    <img src="https://ztimg.hefei.cc/zt2025/fygxbjxdzxzsq/img/poster.jpg?v=1" class="poster">
                    <div class="p1 text-base" id="p1Container">
                        <!-- 动态生成内容 -->
                    </div>
                </div>
                <div class="flex justify-center space-x-4">
                    <button id="generateCertificateBtn" class="gradient-bg text-white px-4 py-2 rounded-lg text-sm flex items-center space-x-2">
                        <i class="fas fa-magic"></i>
                        <span>生成证书</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 头像编辑器 -->
    <div class="px-6 mb-6">
        <div class="bg-white rounded-2xl p-6 shadow-lg">
            <h3 class="text-xl font-bold text-gray-800 mb-2 flex items-center">
                <i class="fas fa-user-edit text-blue-500 mr-3"></i>
                荣誉头像制作
            </h3>
            <p class="text-sm text-gray-600 mb-4">选择图片后可缩放及拖动调整位置</p>

            <div class="flex flex-col items-center space-y-4">
                <!-- 头像编辑区域 -->
                <div class="avatar-editor-container">
                    <canvas id="avatarCanvas" class="avatar-canvas"></canvas>
                </div>

                <!-- 头像框样式选择 -->
                <div class="w-full">
                    <h4 class="text-sm font-semibold text-gray-700 mb-3 text-center">选择头像框</h4>
                    <div class="grid grid-cols-4 gap-3 max-w-xs mx-auto">
                        <button id="frameBtn1" class="frame-btn group bg-white border-2 border-gray-200 rounded-xl p-1 flex items-center justify-center hover:border-blue-500 hover:shadow-lg transition-all duration-300" disabled>
                            <img src="https://ztimg.hefei.cc/zt2025/fygxbjxdzxzsq/img/box1.png" alt="头像框1" class="frame-preview-img w-12 h-12 object-cover rounded">
                        </button>
                        <button id="frameBtn3" class="frame-btn group bg-white border-2 border-gray-200 rounded-xl p-1 flex items-center justify-center hover:border-blue-500 hover:shadow-lg transition-all duration-300" disabled>
                            <img src="https://ztimg.hefei.cc/zt2025/fygxbjxdzxzsq/img/box3.png" alt="头像框3" class="frame-preview-img w-12 h-12 object-cover rounded">
                        </button>
                        <button id="frameBtn5" class="frame-btn group bg-white border-2 border-gray-200 rounded-xl p-1 flex items-center justify-center hover:border-blue-500 hover:shadow-lg transition-all duration-300" disabled>
                            <img src="https://ztimg.hefei.cc/zt2025/fygxbjxdzxzsq/img/box5.png" alt="头像框5" class="frame-preview-img w-12 h-12 object-cover rounded">
                        </button>
                        <button id="frameBtn7" class="frame-btn group bg-white border-2 border-gray-200 rounded-xl p-1 flex items-center justify-center hover:border-blue-500 hover:shadow-lg transition-all duration-300" disabled>
                            <img src="https://ztimg.hefei.cc/zt2025/fygxbjxdzxzsq/img/box7.png" alt="头像框7" class="frame-preview-img w-12 h-12 object-cover rounded">
                        </button>
                        <button id="frameBtn2" class="frame-btn group bg-white border-2 border-gray-200 rounded-xl p-1 flex items-center justify-center hover:border-blue-500 hover:shadow-lg transition-all duration-300" disabled>
                            <img src="https://ztimg.hefei.cc/zt2025/fygxbjxdzxzsq/img/box2.png" alt="头像框2" class="frame-preview-img w-12 h-12 object-cover rounded">
                        </button>
                        <button id="frameBtn4" class="frame-btn group bg-white border-2 border-gray-200 rounded-xl p-1 flex items-center justify-center hover:border-blue-500 hover:shadow-lg transition-all duration-300" disabled>
                            <img src="https://ztimg.hefei.cc/zt2025/fygxbjxdzxzsq/img/box4.png" alt="头像框4" class="frame-preview-img w-12 h-12 object-cover rounded">
                        </button>
                        <button id="frameBtn6" class="frame-btn group bg-white border-2 border-gray-200 rounded-xl p-1 flex items-center justify-center hover:border-blue-500 hover:shadow-lg transition-all duration-300" disabled>
                            <img src="https://ztimg.hefei.cc/zt2025/fygxbjxdzxzsq/img/box6.png" alt="头像框6" class="frame-preview-img w-12 h-12 object-cover rounded">
                        </button>
                        <button id="frameBtn8" class="frame-btn group bg-white border-2 border-gray-200 rounded-xl p-1 flex items-center justify-center hover:border-blue-500 hover:shadow-lg transition-all duration-300" disabled>
                            <img src="https://ztimg.hefei.cc/zt2025/fygxbjxdzxzsq/img/box8.png" alt="头像框8" class="frame-preview-img w-12 h-12 object-cover rounded">
                        </button>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex space-x-2 w-full">
                    <button id="uploadBtn" class="flex-1 bg-blue-500 text-white py-3 rounded-xl flex flex-col items-center justify-center hover:bg-blue-600 transition-colors">
                        <i class="fas fa-image text-lg mb-1"></i>
                        <span class="text-sm">选择图片</span>
                    </button>

                    <button id="wechatBtn" class="flex-1 bg-green-500 text-white py-3 rounded-xl flex flex-col items-center justify-center hover:bg-green-600 transition-colors">
                        <i class="fab fa-weixin text-lg mb-1"></i>
                        <span class="text-sm">微信头像</span>
                    </button>

                    <button id="generateBtn" class="flex-1 gradient-bg text-white py-3 rounded-xl flex flex-col items-center justify-center hover:opacity-90 transition-opacity" disabled>
                        <i class="fas fa-magic text-lg mb-1"></i>
                        <span class="text-sm">生成头像</span>
                    </button>
                </div>


            </div>
        </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input type="file" id="fileInput" accept="image/*" style="display: none;">
        <!-- 底部提示 -->
        <div class="mt-6 text-center mb-6">
            <p class="text-xs text-gray-500">
                <i class="fas fa-shield-alt mr-1"></i>
                您的图片不会被上传，全部本地处理
            </p>
        </div>
    </div> <!-- 结束主要内容容器 -->
    <!-- 生成结果模态框 -->
    <div id="resultModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
        <div class="bg-white rounded-2xl p-6 m-4 max-w-sm w-full">
            <h3 id="resultModalTitle" class="text-xl font-bold text-gray-800 mb-4 text-center">
                <i class="fas fa-check-circle text-green-500 mr-2"></i>
                生成成功
            </h3>
            <div class="flex justify-center mb-4">
                <img id="resultImage" class="border-2 border-gray-200 rounded-lg shadow-lg" style="max-width: 220px; max-height: 220px;" alt="生成结果">
            </div>
            <div class="text-center mb-4">
                <p class="text-gray-600 mb-2">
                    <i class="fas fa-hand-pointer text-blue-500 mr-1"></i>
                    长按图片保存到相册
                </p>
                <!-- <p class="text-xs text-gray-500">
                    <i class="fas fa-info-circle mr-1"></i>
                    适用于微信、QQ等社交平台头像
                </p> -->
            </div>
            <div class="flex justify-center">
                <button id="closeModalBtn" class="bg-gray-500 text-white px-8 py-3 rounded-xl hover:bg-gray-600 transition-colors">
                    <i class="fas fa-times mr-2"></i>
                    关闭
                </button>
            </div>
        </div>
    </div>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/html2canvas.min.js"></script>
    <script>
        var avatar = "{$avatar}";
        var startData = {
            name:'{$name}',
            rank:'{$rank}'
        }
        /**
         * 进入动画控制脚本
         * 安徽省非血缘造血干细胞捐献电子荣誉勋章申领系统
         */

        class EnterAnimation {
            constructor() {
                this.animationElement = document.getElementById('enterAnimation');
                this.mainContent = document.getElementById('mainContent');
                this.loadingText = document.querySelector('.loading-text');
                this.artTextImage = document.querySelector('.art-text-image');
                
                this.loadingTexts = [
                    '正在加载...',
                    '初始化系统...',
                    '验证身份信息...',
                    '准备荣誉证书...',
                    '即将进入系统...'
                ];
                
                this.currentTextIndex = 0;
                this.init();
            }
            
            init() {
                // 确保页面完全加载后开始动画
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', () => {
                        this.startAnimation();
                    });
                } else {
                    this.startAnimation();
                }
            }
            
            startAnimation() {
                // 开始加载文字轮播
                this.startLoadingTextRotation();
                
                // 添加艺术字的额外交互效果
                this.addArtTextInteraction();
                
                // 设置动画完成时间（总共4秒）
                setTimeout(() => {
                    this.completeAnimation();
                }, 4000);
            }
            
            startLoadingTextRotation() {
                const rotateText = () => {
                    if (this.currentTextIndex < this.loadingTexts.length - 1) {
                        this.currentTextIndex++;
                        this.loadingText.style.opacity = '0';
                        
                        setTimeout(() => {
                            this.loadingText.textContent = this.loadingTexts[this.currentTextIndex];
                            this.loadingText.style.opacity = '1';
                        }, 200);
                        
                        setTimeout(rotateText, 800);
                    }
                };
                
                setTimeout(rotateText, 800);
            }
            
            addArtTextInteraction() {
                // 添加鼠标悬停效果（如果是桌面设备）
                if (!this.isMobile()) {
                    this.artTextImage.addEventListener('mouseenter', () => {
                        this.artTextImage.style.transform = 'scale(1.1) rotate(5deg)';
                        this.artTextImage.style.filter = 'drop-shadow(0 15px 30px rgba(0, 0, 0, 0.5)) brightness(1.2)';
                    });
                    
                    this.artTextImage.addEventListener('mouseleave', () => {
                        this.artTextImage.style.transform = 'scale(1) rotate(0deg)';
                        this.artTextImage.style.filter = 'drop-shadow(0 10px 20px rgba(0, 0, 0, 0.3)) brightness(1)';
                    });
                }
                
                // 添加点击效果
                this.artTextImage.addEventListener('click', () => {
                    this.triggerClickEffect();
                });
            }
            
            triggerClickEffect() {
                // 创建点击波纹效果
                const ripple = document.createElement('div');
                ripple.style.cssText = `
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    width: 0;
                    height: 0;
                    background: rgba(255, 255, 255, 0.6);
                    border-radius: 50%;
                    transform: translate(-50%, -50%);
                    animation: clickRipple 0.6s ease-out;
                    pointer-events: none;
                    z-index: 10;
                `;
                
                // 添加波纹动画样式
                if (!document.getElementById('clickRippleStyle')) {
                    const style = document.createElement('style');
                    style.id = 'clickRippleStyle';
                    style.textContent = `
                        @keyframes clickRipple {
                            to {
                                width: 200px;
                                height: 200px;
                                opacity: 0;
                            }
                        }
                    `;
                    document.head.appendChild(style);
                }
                
                this.artTextImage.parentElement.style.position = 'relative';
                this.artTextImage.parentElement.appendChild(ripple);
                
                // 移除波纹元素
                setTimeout(() => {
                    ripple.remove();
                }, 600);
                
                // 艺术字震动效果
                this.artTextImage.style.animation = 'none';
                setTimeout(() => {
                    this.artTextImage.style.animation = 'floatAndGlow 3s ease-in-out infinite, artTextShake 0.5s ease-in-out';
                }, 10);
                
                // 添加震动动画
                if (!document.getElementById('artTextShakeStyle')) {
                    const shakeStyle = document.createElement('style');
                    shakeStyle.id = 'artTextShakeStyle';
                    shakeStyle.textContent = `
                        @keyframes artTextShake {
                            0%, 100% { transform: translateX(0) translateY(0); }
                            25% { transform: translateX(-5px) translateY(-2px); }
                            50% { transform: translateX(5px) translateY(2px); }
                            75% { transform: translateX(-3px) translateY(-1px); }
                        }
                    `;
                    document.head.appendChild(shakeStyle);
                }
            }
            
            completeAnimation() {
                // 更新加载文字为完成状态
                this.loadingText.style.opacity = '0';
                setTimeout(() => {
                    this.loadingText.textContent = '加载完成！';
                    this.loadingText.style.opacity = '1';
                }, 200);
                
                // 1秒后开始退出动画
                setTimeout(() => {
                    this.exitAnimation();
                }, 1000);
            }
            
            exitAnimation() {
                // 添加退出动画类
                this.animationElement.classList.add('fade-out');
                
                // 同时显示主要内容
                setTimeout(() => {
                    this.mainContent.classList.add('show');
                }, 200);
                
                // 动画完成后移除动画元素
                setTimeout(() => {
                    this.animationElement.style.display = 'none';
                    // 触发自定义事件，通知其他脚本动画已完成
                    window.dispatchEvent(new CustomEvent('enterAnimationComplete'));
                }, 800);
            }
            
            isMobile() {
                return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            }
            
            // 提供手动跳过动画的方法
            skipAnimation() {
                this.animationElement.style.transition = 'opacity 0.3s ease';
                this.animationElement.style.opacity = '0';
                
                setTimeout(() => {
                    this.animationElement.style.display = 'none';
                    this.mainContent.classList.add('show');
                    window.dispatchEvent(new CustomEvent('enterAnimationComplete'));
                }, 300);
            }
        }

        // 初始化进入动画
        document.addEventListener('DOMContentLoaded', () => {
            const enterAnimation = new EnterAnimation();
            
            // 添加跳过动画的快捷键（按ESC键）
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && document.getElementById('enterAnimation').style.display !== 'none') {
                    enterAnimation.skipAnimation();
                }
            });
            
            // 添加双击跳过功能
            let clickCount = 0;
            document.addEventListener('click', () => {
                clickCount++;
                setTimeout(() => {
                    if (clickCount === 2 && document.getElementById('enterAnimation').style.display !== 'none') {
                        enterAnimation.skipAnimation();
                    }
                    clickCount = 0;
                }, 300);
            });
        });

        // 导出到全局作用域，以便其他脚本使用
        window.EnterAnimation = EnterAnimation;

    </script>
    <script src="js/avatar-editor.js?v={php} echo mt_rand(1000,999999);{/php}"></script>
    <script src="js/app.js?v={php} echo mt_rand(1000,999999);{/php}"></script>
    {include file="share"/}
</body>
</html>
