// Canvas头像编辑器
class AvatarEditor {
    constructor(canvasId) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.container = document.querySelector('.avatar-editor-container');

        // 获取设备像素比，确保高DPI屏幕清晰度
        this.devicePixelRatio = window.devicePixelRatio || 1;

        // 获取容器的内部尺寸（减去边框）
        const containerStyle = window.getComputedStyle(this.container);
        const borderWidth = parseInt(containerStyle.borderLeftWidth) + parseInt(containerStyle.borderRightWidth);
        const containerWidth = this.container.offsetWidth - borderWidth;

        // 设置canvas尺寸（考虑设备像素比）
        this.canvasSize = containerWidth;
        this.canvas.width = this.canvasSize * this.devicePixelRatio;
        this.canvas.height = this.canvasSize * this.devicePixelRatio;

        // 设置CSS尺寸
        this.canvas.style.width = this.canvasSize + 'px';
        this.canvas.style.height = this.canvasSize + 'px';

        // 缩放上下文以匹配设备像素比
        this.ctx.scale(this.devicePixelRatio, this.devicePixelRatio);

        // 设置图像平滑选项以获得更好的质量
        this.ctx.imageSmoothingEnabled = true;
        this.ctx.imageSmoothingQuality = 'high';
        
        // 图片相关
        this.image = null;
        this.imageLoaded = false;
        
        // 变换参数
        this.scale = 1;
        this.offsetX = 0;
        this.offsetY = 0;
        this.minScale = 1;
        
        // 交互状态
        this.isDragging = false;
        this.isScaling = false;
        this.lastX = 0;
        this.lastY = 0;
        this.initialDistance = 0;
        this.initialScale = 1;

        // 滚轮缩放回弹定时器
        this.wheelTimeout = null;

        // 触摸缩放回弹定时器
        this.scaleTimeout = null;

        // 头像框相关
        this.frameType = null; // 当前选择的头像框类型 (动态检测)
        this.frameImages = {}; // 存储加载的头像框图片
        this.frameImagesLoaded = false;

        // 预加载头像框图片
        this.loadFrameImages();
        
        this.initEvents();
        this.draw();
    }

    // 预加载头像框图片
    loadFrameImages() {
        // 自动检测页面中有多少个头像框按钮
        const frameButtons = document.querySelectorAll('[id^="frameBtn"]');
        const frameCount = frameButtons.length;
        let loadedCount = 0;

        if (frameCount === 0) {
            this.frameImagesLoaded = true;
            return;
        }

        for (let i = 1; i <= frameCount; i++) {
            const img = new Image();
            img.crossOrigin = 'anonymous'; // 设置CORS属性以避免canvas污染
            img.onload = () => {
                loadedCount++;
                if (loadedCount === frameCount) {
                    this.frameImagesLoaded = true;
                    this.draw(); // 重新绘制
                }
            };
            img.onerror = () => {
                console.warn(`Failed to load frame image from CDN: box${i}.png, trying local fallback`);
                // 尝试加载本地图片作为备选方案
                const fallbackImg = new Image();
                fallbackImg.onload = () => {
                    this.frameImages[i] = fallbackImg;
                    loadedCount++;
                    if (loadedCount === frameCount) {
                        this.frameImagesLoaded = true;
                        this.draw();
                    }
                };
                fallbackImg.onerror = () => {
                    console.warn(`Failed to load local fallback frame image: box${i}.png`);
                    loadedCount++;
                    if (loadedCount === frameCount) {
                        this.frameImagesLoaded = true;
                        this.draw();
                    }
                };
                fallbackImg.src = `img/box${i}.png`; // 本地图片路径
            };
            img.src = `https://ztimg.hefei.cc/zt2025/fygxbjxdzxzsq/img/box${i}.png`;
            this.frameImages[i] = img;
        }
    }

    // 加载图片
    loadImage(src) {
        this.image = new Image();
        this.image.onload = () => {
            this.imageLoaded = true;
            this.calculateMinScale();
            this.resetTransform();
            this.draw();
        };
        // 设置图片加载属性以获得更好的质量
        this.image.crossOrigin = 'anonymous';
        this.image.src = src;
    }
    
    // 计算最小缩放比例（确保图片填满canvas）
    calculateMinScale() {
        if (!this.image) return;
        
        const scaleX = this.canvasSize / this.image.width;
        const scaleY = this.canvasSize / this.image.height;
        this.minScale = Math.max(scaleX, scaleY);
    }
    
    // 重置变换
    resetTransform() {
        this.scale = this.minScale;
        this.offsetX = 0;
        this.offsetY = 0;
        this.constrainTransform();
    }
    
    // 约束变换参数（拖拽时不约束，允许空白）
    constrainTransform(allowEmpty = true) {
        if (!this.image) return;

        // 约束缩放范围（最小0.1倍，最大5倍）
        this.scale = Math.max(0.1, Math.min(5, this.scale));

        if (!allowEmpty) {
            // 松手后的约束：确保填满画布
            this.scale = Math.max(this.minScale, this.scale);

            // 计算图片显示尺寸
            const displayWidth = this.image.width * this.scale;
            const displayHeight = this.image.height * this.scale;

            // 约束偏移，确保不出现空白
            const maxOffsetX = Math.max(0, (displayWidth - this.canvasSize) / 2);
            const maxOffsetY = Math.max(0, (displayHeight - this.canvasSize) / 2);

            this.offsetX = Math.max(-maxOffsetX, Math.min(maxOffsetX, this.offsetX));
            this.offsetY = Math.max(-maxOffsetY, Math.min(maxOffsetY, this.offsetY));
        }
    }
    
    // 绘制
    draw() {
        // 清空canvas
        this.ctx.clearRect(0, 0, this.canvasSize, this.canvasSize);

        // 绘制背景
        this.ctx.fillStyle = '#f9fafb';
        this.ctx.fillRect(0, 0, this.canvasSize, this.canvasSize);

        if (this.imageLoaded && this.image) {
            // 保存上下文
            this.ctx.save();

            // 应用变换
            this.ctx.translate(this.canvasSize / 2, this.canvasSize / 2);
            this.ctx.scale(this.scale, this.scale);
            this.ctx.translate(this.offsetX / this.scale, this.offsetY / this.scale);

            // 绘制图片
            this.ctx.drawImage(
                this.image,
                -this.image.width / 2,
                -this.image.height / 2,
                this.image.width,
                this.image.height
            );

            // 恢复上下文
            this.ctx.restore();
        } else {
            // 绘制上传提示
            // this.drawUploadPlaceholder();
        }

        // 绘制头像框（在图片之上）
        this.drawFrame();
    }
    
    // 绘制上传提示
    drawUploadPlaceholder() {
        this.ctx.fillStyle = '#3b82f6';
        this.ctx.font = '16px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText('点击上传图片', this.canvasSize / 2, this.canvasSize / 2 - 10);
        
        this.ctx.fillStyle = '#9ca3af';
        this.ctx.font = '12px Arial';
        this.ctx.fillText('支持JPG、PNG格式', this.canvasSize / 2, this.canvasSize / 2 + 15);
    }
    
    // 绘制头像框
    drawFrame() {
        if (!this.frameType || !this.frameImagesLoaded) return;

        const frameImage = this.frameImages[this.frameType];
        if (!frameImage || !frameImage.complete) return;

        this.ctx.save();

        // 绘制头像框图片，填满整个canvas
        this.ctx.drawImage(frameImage, 0, 0, this.canvasSize, this.canvasSize);

        this.ctx.restore();
    }
    
    // 设置头像框
    setFrame(frameType) {
        this.frameType = frameType;
        this.draw();
    }
    
    // 清除所有内容
    reset() {
        this.image = null;
        this.imageLoaded = false;
        this.frameType = null;
        this.scale = 1;
        this.offsetX = 0;
        this.offsetY = 0;
        this.draw();
    }
    
    // 获取画布内容（高质量输出）
    getImageData(quality = 1.0) {
        try {
            // 创建一个临时的高分辨率canvas用于输出
            const outputCanvas = document.createElement('canvas');
            const outputSize = 512; // 输出更高分辨率
            outputCanvas.width = outputSize;
            outputCanvas.height = outputSize;

            const outputCtx = outputCanvas.getContext('2d');

            // 设置高质量渲染选项
            outputCtx.imageSmoothingEnabled = true;
            outputCtx.imageSmoothingQuality = 'high';

            // 清空并设置背景
            outputCtx.fillStyle = '#f9fafb';
            outputCtx.fillRect(0, 0, outputSize, outputSize);

            if (this.imageLoaded && this.image) {
                // 保存上下文
                outputCtx.save();

                // 计算缩放比例（从当前canvas尺寸到输出尺寸）
                const scale = outputSize / this.canvasSize;

                // 应用变换
                outputCtx.translate(outputSize / 2, outputSize / 2);
                outputCtx.scale(this.scale * scale, this.scale * scale);
                outputCtx.translate(this.offsetX / this.scale, this.offsetY / this.scale);

                // 绘制图片
                outputCtx.drawImage(
                    this.image,
                    -this.image.width / 2,
                    -this.image.height / 2,
                    this.image.width,
                    this.image.height
                );

                // 恢复上下文
                outputCtx.restore();
            }

            // 绘制头像框（按比例缩放）
            if (this.frameType && this.frameImagesLoaded) {
                const frameImage = this.frameImages[this.frameType];
                if (frameImage && frameImage.complete) {
                    outputCtx.save();

                    // 绘制头像框图片，填满整个输出canvas
                    outputCtx.drawImage(frameImage, 0, 0, outputSize, outputSize);

                    outputCtx.restore();
                }
            }

            // 返回高质量图像数据
            return outputCanvas.toDataURL('image/png', quality);
        } catch (error) {
            console.error('Canvas toDataURL failed:', error);
            // 如果出现CORS错误，尝试使用原始canvas
            try {
                return this.canvas.toDataURL('image/png', quality);
            } catch (fallbackError) {
                console.error('Fallback canvas toDataURL also failed:', fallbackError);
                // 最后的备选方案：返回一个错误提示
                throw new Error('无法导出图像：可能是由于跨域限制。请确保所有图像资源都支持CORS访问。');
            }
        }
    }

    // 初始化事件
    initEvents() {
        // 鼠标事件
        this.canvas.addEventListener('mousedown', this.onMouseDown.bind(this));
        this.canvas.addEventListener('mousemove', this.onMouseMove.bind(this));
        this.canvas.addEventListener('mouseup', this.onMouseUp.bind(this));
        this.canvas.addEventListener('wheel', this.onWheel.bind(this));

        // 触摸事件
        this.canvas.addEventListener('touchstart', this.onTouchStart.bind(this));
        this.canvas.addEventListener('touchmove', this.onTouchMove.bind(this));
        this.canvas.addEventListener('touchend', this.onTouchEnd.bind(this));



        // 防止默认行为
        this.canvas.addEventListener('contextmenu', e => e.preventDefault());
    }

    // 获取鼠标/触摸位置
    getEventPos(e) {
        const rect = this.canvas.getBoundingClientRect();
        const clientX = e.clientX || (e.touches && e.touches[0].clientX);
        const clientY = e.clientY || (e.touches && e.touches[0].clientY);
        return {
            x: clientX - rect.left,
            y: clientY - rect.top
        };
    }

    // 鼠标按下
    onMouseDown(e) {
        e.preventDefault();
        const pos = this.getEventPos(e);

        // 如果有图片，开始拖拽图片
        if (this.imageLoaded) {
            this.isDragging = true;
            this.lastX = pos.x;
            this.lastY = pos.y;
        }
    }

    // 鼠标移动
    onMouseMove(e) {
        if (!this.isDragging || !this.imageLoaded) return;
        e.preventDefault();

        const pos = this.getEventPos(e);
        const deltaX = pos.x - this.lastX;
        const deltaY = pos.y - this.lastY;

        // 拖拽图片 - 允许空白
        this.offsetX += deltaX;
        this.offsetY += deltaY;
        this.constrainTransform(true); // 拖拽时允许空白

        this.lastX = pos.x;
        this.lastY = pos.y;
        this.draw();
    }

    // 鼠标抬起
    onMouseUp() {
        if (this.isDragging && this.imageLoaded) {
            // 松手后回弹，不允许空白
            this.snapToFillCanvas();
        }
        this.isDragging = false;
    }

    // 滚轮缩放
    onWheel(e) {
        if (!this.imageLoaded) return;
        e.preventDefault();

        const scaleFactor = e.deltaY > 0 ? 0.9 : 1.1;
        this.scale *= scaleFactor;
        this.constrainTransform(true); // 缩放时允许空白
        this.draw();

        // 清除之前的定时器
        if (this.wheelTimeout) {
            clearTimeout(this.wheelTimeout);
        }

        // 设置延迟回弹（滚轮停止后300ms回弹）
        this.wheelTimeout = setTimeout(() => {
            this.snapToFillCanvas();
            this.wheelTimeout = null;
        }, 300);
    }



    // 触摸开始
    onTouchStart(e) {
        e.preventDefault();

        if (e.touches.length === 1) {
            // 单指触摸，等同于鼠标按下
            this.onMouseDown(e);
        } else if (e.touches.length === 2 && this.imageLoaded) {
            // 双指触摸，开始缩放
            this.isScaling = true;
            this.isDragging = false;

            const touch1 = e.touches[0];
            const touch2 = e.touches[1];
            this.initialDistance = this.getTouchDistance(touch1, touch2);
            this.initialScale = this.scale;
        }
    }

    // 触摸移动
    onTouchMove(e) {
        e.preventDefault();

        if (e.touches.length === 1 && !this.isScaling) {
            // 单指拖拽
            this.onMouseMove(e);
        } else if (e.touches.length === 2 && this.isScaling && this.imageLoaded) {
            // 双指缩放
            const touch1 = e.touches[0];
            const touch2 = e.touches[1];
            const currentDistance = this.getTouchDistance(touch1, touch2);

            if (this.initialDistance > 0) {
                const scaleChange = currentDistance / this.initialDistance;
                this.scale = this.initialScale * scaleChange;
                this.constrainTransform(true); // 缩放时允许空白
                this.draw();

                // 清除之前的定时器
                if (this.scaleTimeout) {
                    clearTimeout(this.scaleTimeout);
                }

                // 设置延迟回弹（缩放停止后200ms回弹）
                this.scaleTimeout = setTimeout(() => {
                    if (this.isScaling) {
                        this.snapToFillCanvas();
                    }
                    this.scaleTimeout = null;
                }, 200);
            }
        }
    }

    // 触摸结束
    onTouchEnd(e) {
        if (e.touches.length === 0) {
            // 所有手指离开
            // 清除缩放定时器
            if (this.scaleTimeout) {
                clearTimeout(this.scaleTimeout);
                this.scaleTimeout = null;
            }

            if ((this.isDragging || this.isScaling) && this.imageLoaded) {
                // 松手后回弹，不允许空白
                this.snapToFillCanvas();
            }
            this.isDragging = false;
            this.isScaling = false;
        } else if (e.touches.length === 1 && this.isScaling) {
            // 从双指变为单指，先回弹
            // 清除缩放定时器
            if (this.scaleTimeout) {
                clearTimeout(this.scaleTimeout);
                this.scaleTimeout = null;
            }

            if (this.imageLoaded) {
                this.snapToFillCanvas();
            }
            this.isScaling = false;
            // 重新开始单指操作
            this.onMouseDown(e);
        }
    }

    // 计算两点距离
    getTouchDistance(touch1, touch2) {
        const dx = touch1.clientX - touch2.clientX;
        const dy = touch1.clientY - touch2.clientY;
        return Math.sqrt(dx * dx + dy * dy);
    }



    // 回弹到填满画布
    snapToFillCanvas() {
        if (!this.image) return;

        const oldScale = this.scale;
        const oldOffsetX = this.offsetX;
        const oldOffsetY = this.offsetY;

        // 应用约束（不允许空白）
        this.constrainTransform(false);

        // 检查是否需要动画
        const needsAnimation = (
            Math.abs(this.scale - oldScale) > 0.01 ||
            Math.abs(this.offsetX - oldOffsetX) > 1 ||
            Math.abs(this.offsetY - oldOffsetY) > 1
        );

        if (needsAnimation) {
            // 执行平滑动画
            this.animateToTarget(oldScale, oldOffsetX, oldOffsetY, this.scale, this.offsetX, this.offsetY);
        }
    }

    // 平滑动画到目标位置
    animateToTarget(fromScale, fromX, fromY, toScale, toX, toY) {
        const duration = 300; // 动画时长
        const startTime = Date.now();

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // 使用缓动函数
            const easeProgress = 1 - Math.pow(1 - progress, 3);

            // 插值计算当前值
            this.scale = fromScale + (toScale - fromScale) * easeProgress;
            this.offsetX = fromX + (toX - fromX) * easeProgress;
            this.offsetY = fromY + (toY - fromY) * easeProgress;

            this.draw();

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        animate();
    }
}
