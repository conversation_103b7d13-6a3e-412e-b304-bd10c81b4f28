<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0,minimal-ui:ios" name="viewport">
    <title>{$zt_name}</title>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/tailwindcss.js"></script>
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/fontawesome-free-5.15.4-web/css/all.min.css">
    <link rel="stylesheet" href="css/index.css?v={php} echo mt_rand(1000,999999);{/php}">
</head>
<body class="bg-gray-50">
    <!-- 头像编辑器 -->
    <div class="px-6 py-6">
        <div class="bg-white rounded-2xl p-6 shadow-lg">
            <h3 class="text-xl font-bold text-gray-800 mb-2 flex items-center">
                <i class="fas fa-user-edit text-blue-500 mr-3"></i>
                荣誉头像制作
            </h3>
            <p class="text-sm text-gray-600 mb-4">选择图片后可缩放及拖动调整位置</p>

            <div class="flex flex-col items-center space-y-4">
                <!-- 头像编辑区域 -->
                <div class="avatar-editor-container">
                    <canvas id="avatarCanvas" class="avatar-canvas"></canvas>
                </div>

                <!-- 头像框样式选择 -->
                <div class="w-full">
                    <h4 class="text-sm font-semibold text-gray-700 mb-3 text-center">选择头像框</h4>
                    <div class="grid grid-cols-4 gap-3 max-w-xs mx-auto">
                        <button id="frameBtn1" class="frame-btn group bg-white border-2 border-gray-200 rounded-xl p-1 flex items-center justify-center hover:border-blue-500 hover:shadow-lg transition-all duration-300" disabled>
                            <img src="https://ztimg.hefei.cc/zt2025/fygxbjxdzxzsq/img/box1.png" alt="头像框1" class="frame-preview-img w-12 h-12 object-cover rounded">
                        </button>
                        <button id="frameBtn3" class="frame-btn group bg-white border-2 border-gray-200 rounded-xl p-1 flex items-center justify-center hover:border-blue-500 hover:shadow-lg transition-all duration-300" disabled>
                            <img src="https://ztimg.hefei.cc/zt2025/fygxbjxdzxzsq/img/box3.png" alt="头像框3" class="frame-preview-img w-12 h-12 object-cover rounded">
                        </button>
                        <button id="frameBtn5" class="frame-btn group bg-white border-2 border-gray-200 rounded-xl p-1 flex items-center justify-center hover:border-blue-500 hover:shadow-lg transition-all duration-300" disabled>
                            <img src="https://ztimg.hefei.cc/zt2025/fygxbjxdzxzsq/img/box5.png" alt="头像框5" class="frame-preview-img w-12 h-12 object-cover rounded">
                        </button>
                        <button id="frameBtn7" class="frame-btn group bg-white border-2 border-gray-200 rounded-xl p-1 flex items-center justify-center hover:border-blue-500 hover:shadow-lg transition-all duration-300" disabled>
                            <img src="https://ztimg.hefei.cc/zt2025/fygxbjxdzxzsq/img/box7.png" alt="头像框7" class="frame-preview-img w-12 h-12 object-cover rounded">
                        </button>
                        <button id="frameBtn2" class="frame-btn group bg-white border-2 border-gray-200 rounded-xl p-1 flex items-center justify-center hover:border-blue-500 hover:shadow-lg transition-all duration-300" disabled>
                            <img src="https://ztimg.hefei.cc/zt2025/fygxbjxdzxzsq/img/box2.png" alt="头像框2" class="frame-preview-img w-12 h-12 object-cover rounded">
                        </button>
                        <button id="frameBtn4" class="frame-btn group bg-white border-2 border-gray-200 rounded-xl p-1 flex items-center justify-center hover:border-blue-500 hover:shadow-lg transition-all duration-300" disabled>
                            <img src="https://ztimg.hefei.cc/zt2025/fygxbjxdzxzsq/img/box4.png" alt="头像框4" class="frame-preview-img w-12 h-12 object-cover rounded">
                        </button>
                        <button id="frameBtn6" class="frame-btn group bg-white border-2 border-gray-200 rounded-xl p-1 flex items-center justify-center hover:border-blue-500 hover:shadow-lg transition-all duration-300" disabled>
                            <img src="https://ztimg.hefei.cc/zt2025/fygxbjxdzxzsq/img/box6.png" alt="头像框6" class="frame-preview-img w-12 h-12 object-cover rounded">
                        </button>
                        <button id="frameBtn8" class="frame-btn group bg-white border-2 border-gray-200 rounded-xl p-1 flex items-center justify-center hover:border-blue-500 hover:shadow-lg transition-all duration-300" disabled>
                            <img src="https://ztimg.hefei.cc/zt2025/fygxbjxdzxzsq/img/box8.png" alt="头像框8" class="frame-preview-img w-12 h-12 object-cover rounded">
                        </button>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex space-x-2 w-full">
                    <button id="uploadBtn" class="flex-1 bg-blue-500 text-white py-3 rounded-xl flex flex-col items-center justify-center hover:bg-blue-600 transition-colors">
                        <i class="fas fa-image text-lg mb-1"></i>
                        <span class="text-sm">选择图片</span>
                    </button>

                    <button id="wechatBtn" class="flex-1 bg-green-500 text-white py-3 rounded-xl flex flex-col items-center justify-center hover:bg-green-600 transition-colors">
                        <i class="fab fa-weixin text-lg mb-1"></i>
                        <span class="text-sm">微信头像</span>
                    </button>

                    <button id="generateBtn" class="flex-1 gradient-bg text-white py-3 rounded-xl flex flex-col items-center justify-center hover:opacity-90 transition-opacity" disabled>
                        <i class="fas fa-magic text-lg mb-1"></i>
                        <span class="text-sm">生成头像</span>
                    </button>
                </div>


            </div>
        </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input type="file" id="fileInput" accept="image/*" style="display: none;">
    <!-- 底部提示 -->
    <div class="mt-6 text-center mb-6">
        <p class="text-xs text-gray-500">
            <i class="fas fa-shield-alt mr-1"></i>
            您的图片不会被上传，全部本地处理
        </p>
    </div>
    <!-- 生成结果模态框 -->
    <div id="resultModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
        <div class="bg-white rounded-2xl p-6 m-4 max-w-sm w-full">
            <h3 id="resultModalTitle" class="text-xl font-bold text-gray-800 mb-4 text-center">
                <i class="fas fa-check-circle text-green-500 mr-2"></i>
                生成成功
            </h3>
            <div class="flex justify-center mb-4">
                <img id="resultImage" class="border-2 border-gray-200 rounded-lg shadow-lg" style="max-width: 220px; max-height: 220px;" alt="生成结果">
            </div>
            <div class="text-center mb-4">
                <p class="text-gray-600 mb-2">
                    <i class="fas fa-hand-pointer text-blue-500 mr-1"></i>
                    长按图片保存到相册
                </p>
                <!-- <p class="text-xs text-gray-500">
                    <i class="fas fa-info-circle mr-1"></i>
                    适用于微信、QQ等社交平台头像
                </p> -->
            </div>
            <div class="flex justify-center">
                <button id="closeModalBtn" class="bg-gray-500 text-white px-8 py-3 rounded-xl hover:bg-gray-600 transition-colors">
                    <i class="fas fa-times mr-2"></i>
                    关闭
                </button>
            </div>
        </div>
    </div>
    <script>
        var avatar = "{$avatar}";
    </script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/html2canvas.min.js"></script>
    <script src="js/avatar-editor.js?v={php} echo mt_rand(1000,999999);{/php}"></script>
    <script src="js/app.js?v={php} echo mt_rand(1000,999999);{/php}"></script>
    {include file="share"/}
</body>
</html>
